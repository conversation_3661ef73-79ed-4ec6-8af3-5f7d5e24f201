apiVersion: v1
kind: Namespace
metadata:
  name: authentica

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: authentica-role
  namespace: authentica
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list", "watch", "create", "update", "delete"]

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: authentica-config
  namespace: authentica
data:
  APP_ENV: "production"
  APP_DEBUG: "false"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: authentica-rolebinding
  namespace: authentica
subjects:
- kind: ServiceAccount
  name: default
  namespace: authentica
roleRef:
  kind: Role
  name: authentica-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: authentica-quota
  namespace: authentica
spec:
  hard:
    requests.cpu: "4"
    requests.memory: "3Gi"
    limits.cpu: "10"
    limits.memory: "8Gi"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: authentica-api-hpa
  namespace: authentica
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: authentica-api
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50

---
apiVersion: v1
kind: Service
metadata:
  name: authentica-api-service
  namespace: authentica
spec:
  type: ClusterIP
  selector:
    app: authentica-api
  ports:
    - port: 7171
      targetPort: 7171

---
apiVersion: v1
kind: Service
metadata:
  name: authentica-api-service
  namespace: authentica
spec:
  type: NodePort
  selector:
    app: authentica-api
  ports:
    - port: 7171
      targetPort: 7171
      nodePort: 30171

{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/Authentica.Service.Identity/bin/Debug/net9.0/Authentica.Service.Identity.dll", "args": [], "cwd": "${workspaceFolder}", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "justMyCode": true}, {"name": ".NET Core Attach", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}", "justMyCode": true}]}
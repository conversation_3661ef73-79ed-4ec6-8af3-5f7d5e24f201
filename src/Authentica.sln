﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Authentica.Common", "Authentica.Common\Authentica.Common.csproj", "{1F13E5C1-1E4F-4663-83AD-9FA0BC7BB261}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Authentica.Service.Identity", "Authentica.Service.Identity\Authentica.Service.Identity.csproj", "{06C9BFF0-ADFD-41CA-BD0A-EC89085C0A77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Authentica.WorkerService.Email", "Authentica.WorkerService.Email\Authentica.WorkerService.Email.csproj", "{A9EB3A1E-DB88-40AC-B2D7-A394FF3238EA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{6840A616-454A-4FDD-A17D-13F0DEDFB6FD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Azure Pipelines", "Azure Pipelines", "{4E8F9E73-82F9-4F2B-AF88-F3A7D9105847}"
	ProjectSection(SolutionItems) = preProject
		service.identity-azure-pipelines-ci.yml = service.identity-azure-pipelines-ci.yml
		workerservice.email-azure-pipelines-ci.yml = workerservice.email-azure-pipelines-ci.yml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Authentica.Service.Identity.Tests", "Authentica.Service.Identity.Tests\Authentica.Service.Identity.Tests.csproj", "{1ECB7655-4638-48B3-AD0C-E0055A2C112A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Authentica.WorkerService.Email.Tests", "Authentica.WorkerService.Email.Tests\Authentica.WorkerService.Email.Tests.csproj", "{9B89482E-177B-48A8-A0CC-DCC1B37BDF81}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Authentica.TestClient", "Authentica.TestClient\Authentica.TestClient.csproj", "{D55C3C93-8743-4BCA-85F9-3E373BA75A82}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Manual", "Manual", "{ACCC80CE-675D-4DAE-9198-8957829965AE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Admin", "Admin", "{85F748FF-0E71-45B8-AC8F-229B77D4B367}"
	ProjectSection(SolutionItems) = preProject
		admin_mfa_disable.http = admin_mfa_disable.http
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Applications", "Applications", "{856A7F1D-B20D-4153-B247-E214127EE5C9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "OAuth", "OAuth", "{4A38B719-FF53-48EE-908E-991C803F2F74}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sessions", "Sessions", "{FEF7AA15-9968-45A4-B5A2-9D622FC8B8F3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Users", "Users", "{3F656E36-9E9B-4D31-9AAA-51B9E0DF7AE7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1F13E5C1-1E4F-4663-83AD-9FA0BC7BB261}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F13E5C1-1E4F-4663-83AD-9FA0BC7BB261}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F13E5C1-1E4F-4663-83AD-9FA0BC7BB261}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F13E5C1-1E4F-4663-83AD-9FA0BC7BB261}.Release|Any CPU.Build.0 = Release|Any CPU
		{06C9BFF0-ADFD-41CA-BD0A-EC89085C0A77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06C9BFF0-ADFD-41CA-BD0A-EC89085C0A77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06C9BFF0-ADFD-41CA-BD0A-EC89085C0A77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06C9BFF0-ADFD-41CA-BD0A-EC89085C0A77}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9EB3A1E-DB88-40AC-B2D7-A394FF3238EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9EB3A1E-DB88-40AC-B2D7-A394FF3238EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9EB3A1E-DB88-40AC-B2D7-A394FF3238EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9EB3A1E-DB88-40AC-B2D7-A394FF3238EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{1ECB7655-4638-48B3-AD0C-E0055A2C112A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1ECB7655-4638-48B3-AD0C-E0055A2C112A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1ECB7655-4638-48B3-AD0C-E0055A2C112A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1ECB7655-4638-48B3-AD0C-E0055A2C112A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B89482E-177B-48A8-A0CC-DCC1B37BDF81}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B89482E-177B-48A8-A0CC-DCC1B37BDF81}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B89482E-177B-48A8-A0CC-DCC1B37BDF81}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B89482E-177B-48A8-A0CC-DCC1B37BDF81}.Release|Any CPU.Build.0 = Release|Any CPU
		{D55C3C93-8743-4BCA-85F9-3E373BA75A82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D55C3C93-8743-4BCA-85F9-3E373BA75A82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D55C3C93-8743-4BCA-85F9-3E373BA75A82}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D55C3C93-8743-4BCA-85F9-3E373BA75A82}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1ECB7655-4638-48B3-AD0C-E0055A2C112A} = {6840A616-454A-4FDD-A17D-13F0DEDFB6FD}
		{9B89482E-177B-48A8-A0CC-DCC1B37BDF81} = {6840A616-454A-4FDD-A17D-13F0DEDFB6FD}
		{D55C3C93-8743-4BCA-85F9-3E373BA75A82} = {6840A616-454A-4FDD-A17D-13F0DEDFB6FD}
		{ACCC80CE-675D-4DAE-9198-8957829965AE} = {6840A616-454A-4FDD-A17D-13F0DEDFB6FD}
		{85F748FF-0E71-45B8-AC8F-229B77D4B367} = {ACCC80CE-675D-4DAE-9198-8957829965AE}
		{856A7F1D-B20D-4153-B247-E214127EE5C9} = {ACCC80CE-675D-4DAE-9198-8957829965AE}
		{4A38B719-FF53-48EE-908E-991C803F2F74} = {ACCC80CE-675D-4DAE-9198-8957829965AE}
		{FEF7AA15-9968-45A4-B5A2-9D622FC8B8F3} = {ACCC80CE-675D-4DAE-9198-8957829965AE}
		{3F656E36-9E9B-4D31-9AAA-51B9E0DF7AE7} = {ACCC80CE-675D-4DAE-9198-8957829965AE}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DB40C019-AC37-4006-9415-18B5F28CC52D}
	EndGlobalSection
EndGlobal

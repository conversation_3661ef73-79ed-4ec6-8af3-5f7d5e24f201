{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"AzureServiceBus": "{azServiceBus}"}, "ApplicationInsights": {"ConnectionString": "{azAppInsightsConnectionString}"}, "FeatureManagement": {"AppInsights": false, "ServiceBus": false, "RabbitMq": false}, "RabbitMQ": {"Hostname": "{rabbitHost}", "Username": "{rabbitUsername}", "Password": "{rabbitPassword}"}, "Email": {"Server": "{emailServer}", "Port": "{emailPort}", "Credentials": {"EmailAddress": "{emailAddress}", "Password": "{emailPassword}"}}}
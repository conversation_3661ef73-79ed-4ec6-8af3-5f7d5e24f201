<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-Authentica.WorkerService.Email-fd181dd0-94a1-43a8-bb7e-5aca25441201</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ChristopherBriddock.AspNetCore.Extensions" Version="9.0.1" />
    <PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.5.1" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.1" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.12.1" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Authentica.Common\Authentica.Common.csproj" />
  </ItemGroup>
</Project>

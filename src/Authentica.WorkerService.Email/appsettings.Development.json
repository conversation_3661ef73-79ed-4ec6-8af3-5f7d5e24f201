{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"AzureServiceBus": ""}, "FeatureManagement": {"AppInsights": false, "ServiceBus": false, "RabbitMq": true}, "RabbitMQ": {"Hostname": "localhost", "Username": "guest", "Password": "guest"}, "Email": {"Server": "{emailServer}", "Port": "{emailPort}", "Credentials": {"EmailAddress": "{emailAddress}", "Password": "{emailPassword}"}}}
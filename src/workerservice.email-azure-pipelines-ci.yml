trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: authentica

stages:
- stage: 'Build'
  displayName: 'Build Worker Service'
  jobs:
  - job: Build
    displayName: 'Build'
    steps:
    - checkout: self
    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: '8.0.x'
    - script: |
        dotnet build --configuration Release src/Authentica.WorkerService.Email/Authentica.WorkerService.Email.csproj
      displayName: 'Build & Restore'
    - script: |
        dotnet test --collect:"XPlat Code Coverage" src/Authentica.WorkerService.Email.Tests/Authentica.WorkerService.Email.Tests.csproj
      displayName: 'Test'
    - script: |
        dotnet publish src/Authentica.WorkerService.Email/Authentica.WorkerService.Email.csproj -c Release
      displayName: 'Publish'
    - script: dotnet tool update --global dotnet-reportgenerator-globaltool
      displayName: 'Install Code Coverage Tool'
    - script: |
        cd src/Authentica.WorkerService.Email.Tests/TestResults
        reportgenerator -reports:"*/coverage.cobertura.xml" -targetdir:"../coveragereport" -reporttypes:Html
      displayName: 'Code Coverage Report'
    - task: PublishCodeCoverageResults@2
      inputs:
        summaryFileLocation: 'src/Authentica.WorkerService.Email.Tests/TestResults/*/coverage.cobertura.xml'
        pathToSources: 'src/Authentica.WorkerService.Email/'
    - task: CopyFiles@2
      inputs:
        SourceFolder: '$(System.DefaultWorkingDirectory)'
        Contents: 'src/Authentica.WorkerService.Email/bin/Release/net8.0/publish/**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
  # - job: Docker
  #   displayName: 'Docker'
  #   dependsOn: 'Build'
  #   steps:
  #   - checkout: self
  #   - task: Bash@3
  #     inputs:
  #       filePath: '$(System.DefaultWorkingDirectory)/src/tokenizer.sh'
  #       arguments: >-
  #         --feature-service-bus '$(ServiceBusEnabled)'
  #         --feature-app-insights '$(ApplicationInsightsEnabled)'
  #         --feature-rabbitmq '$(RabbitEnabled)'
  #         --rabbit-host '$(RabbitHost)'
  #         --rabbit-username '$(RabbitUsername)'
  #         --rabbit-password '$(RabbitPassword)'
  #         --az-app-insights '$(AppInsightsConnectionString)'
  #         --email-server '$(EmailServer)'
  #         --email-port '$(EmailPort)'
  #         --email-address '$(EmailAddress)'
  #         --email-password '$(EmailPassword)'
  #       workingDirectory: '$(System.DefaultWorkingDirectory)/src/Authentica.WorkerService.Email'
  #   - task: Bash@3
  #     inputs:
  #       targetType: 'inline'
  #       script: 'cat appsettings.json'
  #       workingDirectory: '$(System.DefaultWorkingDirectory)/src/Authentica.WorkerService.Email'
  #   - task: Bash@3
  #     inputs:
  #       targetType: 'inline'
  #       script: |
  #         docker build -f $(System.DefaultWorkingDirectory)/src/Dockerfile.workerservice.email -t $(DockerRegistry)/authentica-workerservice-email:latest .
  #       workingDirectory: '$(System.DefaultWorkingDirectory)/src'
  #   - task: Bash@3
  #     inputs: 
  #       targetType: 'inline'
  #       script: docker push $(DockerRegistry)/authentica-workerservice-email:latest
  #       workingDirectory: '$(System.DefaultWorkingDirectory)/src'
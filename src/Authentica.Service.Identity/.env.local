# =============================================================================
# AUTHENTICA CONFIGURATION FILE
# =============================================================================
# This file contains all configuration values used throughout the Authentica
# application. Copy this file to .env and update the values as needed.

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQL Server connection string for the main database
ConnectionStrings__DefaultConnection=Server=localhost,1433;Database=Authentica.Service.Identity;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis connection string for caching and distributed cache
ConnectionStrings__Redis=localhost:6379

# =============================================================================
# AZURE SERVICE BUS CONFIGURATION
# =============================================================================
# Azure Service Bus connection string for messaging
ConnectionStrings__AzureServiceBus=Endpoint=sb://your-servicebus.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# JWT token configuration for authentication
Jwt__Issuer=https://localhost:7171
Jwt__Audience=https://localhost:7171
Jwt__Secret=4YUQmpyxepUHZvEQb738jSba
Jwt__Expires=3600

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
# RabbitMQ configuration for messaging
RabbitMQ__Hostname=localhost
RabbitMQ__Username=guest
RabbitMQ__Password=guest

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP server configuration for email sending
Email__Server=smtp.gmail.com
Email__Port=587
Email__Credentials__EmailAddress=<EMAIL>
Email__Credentials__Password=your-app-password

# =============================================================================
# APPLICATION INSIGHTS CONFIGURATION
# =============================================================================
# Azure Application Insights connection string
ApplicationInsights__ConnectionString=InstrumentationKey=

# =============================================================================
# FEATURE MANAGEMENT
# =============================================================================
# Feature flags to enable/disable various features
FeatureManagement__Cache=true
FeatureManagement__AppInsights=false
FeatureManagement__ServiceBus=false
FeatureManagement__RabbitMq=true

# =============================================================================
# DEFAULT ADMIN CONFIGURATION
# =============================================================================
# Default admin user configuration for production seeding
Defaults__AdminEmail=<EMAIL>
Defaults__AdminPassword=YourSecureAdminPassword123!
Defaults__Secret=b0ahmqtOMNVTnTUJ4E19QNSFe8UYOHqDoO9ovXbNSnRrHjrMbYc1gREBqFOL8XZXuEDFhGamf4Teq7HfXqjMm4kLqjGCg7XAqCjDdUaPSm2HCS2hEL8wR2zD
Defaults__CallbackUri=https://localhost:7256/callback

# =============================================================================
# PASSKEYS CONFIGURATION
# =============================================================================
# WebAuthn/FIDO2 passkey configuration
Passkeys__Origin=https://localhost:7171
Passkeys__Domain=localhost

# =============================================================================
# ASPNET CORE CONFIGURATION
# =============================================================================
# ASP.NET Core specific environment variables
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:7171;http://localhost:5252
ASPNETCORE_HTTPS_PORTS=7171
ASPNETCORE_HTTP_PORTS=5252

# =============================================================================
# OPENTELEMETRY CONFIGURATION
# =============================================================================
# OpenTelemetry exporter endpoint for observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317

# =============================================================================
# KESTREL CERTIFICATE CONFIGURATION
# =============================================================================
# SSL certificate configuration for Kestrel (Docker)
ASPNETCORE_Kestrel__Certificates__Default__Password=YourSecurePassword
ASPNETCORE_Kestrel__Certificates__Default__Path=/app/cert.pfx

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging levels (can be overridden in appsettings.json)
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft.Hosting.Lifetime=Information
Logging__LogLevel__Microsoft.AspNetCore=Warning

# =============================================================================
# DEVELOPMENT SPECIFIC CONFIGURATION
# =============================================================================
# Configuration values specific to development environment
DOTNET_ENVIRONMENT=Development

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CORS origins (hardcoded in code, but can be made configurable)
# Currently allows: http://localhost:3000

# =============================================================================
# NOTES
# =============================================================================
# 1. Replace all placeholder values with your actual configuration values
# 2. Keep this file secure and never commit it to version control
# 3. Use different values for different environments (dev, staging, prod)
# 4. Some values like secrets should be generated uniquely for each environment
# 5. The JWT secret should be a long, random string for security
# 6. Database passwords should be strong and unique
# 7. Email credentials should use app-specific passwords for Gmail

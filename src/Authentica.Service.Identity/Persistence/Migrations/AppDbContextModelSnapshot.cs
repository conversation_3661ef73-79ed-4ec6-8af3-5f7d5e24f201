﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Persistence.Contexts;

#nullable disable

namespace Authentica.Service.Identity.Persistence.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Domain.Aggregates.Identity.Activity", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ActivityType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("activity_type");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedOn")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_on")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("data");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("SequenceId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("sequence_id");

                    b.HasKey("Id");

                    b.ToTable("SYSTEM_IDENTITY_ACTIVITIES", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_ACTIVITIESHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.ClientApplication", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("CallbackUri")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("callback_uri");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("client_id");

                    b.Property<string>("ClientSecret")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("client_secret");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.ClientApplication.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityDeletionStatus", "Domain.Aggregates.Identity.ClientApplication.EntityDeletionStatus#EntityDeletionStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("DeletedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("deleted_by");

                            b1.Property<DateTime?>("DeletedOnUtc")
                                .HasColumnType("datetime2")
                                .HasColumnName("deleted_on_utc");

                            b1.Property<bool>("IsDeleted")
                                .HasColumnType("bit")
                                .HasColumnName("is_deleted");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityModificationStatus", "Domain.Aggregates.Identity.ClientApplication.EntityModificationStatus#EntityModificationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("ModifiedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("modified_by");

                            b1.Property<DateTime?>("ModifiedOnUtc")
                                .ValueGeneratedOnUpdate()
                                .HasColumnType("datetime2")
                                .HasColumnName("modified_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.ToTable("SYSTEM_IDENTITY_CLIENT_APPLICATIONS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_CLIENT_APPLICATIONSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.PasskeyChallenge", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<byte[]>("Challenge")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("challenge");

                    b.Property<string>("ChallengeId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("challenge_id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("ExpiresAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValue(new DateTime(2025, 6, 15, 17, 8, 27, 38, DateTimeKind.Utc).AddTicks(4159))
                        .HasColumnName("expires_at");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("status");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.PasskeyChallenge.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.ToTable("SYSTEM_IDENTITY_USER_PASSKEY_CHALLENGE", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_PASSKEY_CHALLENGEHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.PasskeyCredential", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("AaGuid")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("authenticator_id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_on_utc")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("CredType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("public-key")
                        .HasColumnName("cred_type");

                    b.Property<byte[]>("CredentialId")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("credential_id");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<byte[]>("PublicKey")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("public_key");

                    b.Property<long>("SignatureCounter")
                        .HasColumnType("bigint")
                        .HasColumnName("signature_counter");

                    b.Property<byte[]>("UserHandle")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("user_handle");

                    b.HasKey("Id");

                    b.ToTable("SYSTEM_IDENTITY_USER_PASSKEY_CREDENTIAL", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_PASSKEY_CREDENTIALHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("normalized_name");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.Role.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityDeletionStatus", "Domain.Aggregates.Identity.Role.EntityDeletionStatus#EntityDeletionStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("DeletedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("deleted_by");

                            b1.Property<DateTime?>("DeletedOnUtc")
                                .HasColumnType("datetime2")
                                .HasColumnName("deleted_on_utc");

                            b1.Property<bool>("IsDeleted")
                                .HasColumnType("bit")
                                .HasColumnName("is_deleted");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityModificationStatus", "Domain.Aggregates.Identity.Role.EntityModificationStatus#EntityModificationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("ModifiedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("modified_by");

                            b1.Property<DateTime?>("ModifiedOnUtc")
                                .ValueGeneratedOnUpdate()
                                .HasColumnType("datetime2")
                                .HasColumnName("modified_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[normalized_name] IS NOT NULL");

                    b.ToTable("SYSTEM_IDENTITY_ROLES", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_ROLESHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.RoleClaim", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ClaimType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("claim_value");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("role_id");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("SYSTEM_IDENTITY_ROLE_CLAIMS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_ROLE_CLAIMSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.Session", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime?>("EndDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("end_date_time");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)")
                        .HasColumnName("ip_address");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("session_id");

                    b.Property<DateTime>("StartDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("start_date_time");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("status");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("user_agent");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.Session.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityDeletionStatus", "Domain.Aggregates.Identity.Session.EntityDeletionStatus#EntityDeletionStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("DeletedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("deleted_by");

                            b1.Property<DateTime?>("DeletedOnUtc")
                                .HasMaxLength(36)
                                .HasColumnType("datetime2")
                                .HasColumnName("deleted_on_utc");

                            b1.Property<bool>("IsDeleted")
                                .HasColumnType("bit")
                                .HasColumnName("is_deleted");
                        });

                    b.HasKey("Id");

                    b.ToTable("SYSTEM_IDENTITY_SESSIONS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_SESSIONSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.User", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int")
                        .HasColumnName("access_failed_count");

                    b.Property<string>("ConcurrencyStamp")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("email_confirmed");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NormalizedEmail")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("normalized_username");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("password_hash");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("SecurityStamp")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("security_stamp");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("multi_factor_enabled");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("username");

                    b.ComplexProperty<Dictionary<string, object>>("Address", "Domain.Aggregates.Identity.User.Address#Address", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("City")
                                .HasMaxLength(100)
                                .HasColumnType("nvarchar(100)")
                                .HasColumnName("address_city");

                            b1.Property<string>("Country")
                                .HasMaxLength(100)
                                .HasColumnType("nvarchar(100)")
                                .HasColumnName("address_country");

                            b1.Property<string>("Name")
                                .HasMaxLength(64)
                                .HasColumnType("nvarchar(64)")
                                .HasColumnName("address_name");

                            b1.Property<string>("Number")
                                .HasMaxLength(10)
                                .HasColumnType("nvarchar(10)")
                                .HasColumnName("address_number");

                            b1.Property<string>("Postcode")
                                .HasMaxLength(10)
                                .HasColumnType("nvarchar(10)")
                                .HasColumnName("address_postcode");

                            b1.Property<string>("State")
                                .HasMaxLength(100)
                                .HasColumnType("nvarchar(100)")
                                .HasColumnName("address_state");

                            b1.Property<string>("Street")
                                .HasMaxLength(200)
                                .HasColumnType("nvarchar(200)")
                                .HasColumnName("address_street");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.User.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityDeletionStatus", "Domain.Aggregates.Identity.User.EntityDeletionStatus#EntityDeletionStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("DeletedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("deleted_by");

                            b1.Property<DateTime?>("DeletedOnUtc")
                                .HasColumnType("datetime2")
                                .HasColumnName("deleted_on_utc");

                            b1.Property<bool>("IsDeleted")
                                .HasColumnType("bit")
                                .HasColumnName("is_deleted");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityModificationStatus", "Domain.Aggregates.Identity.User.EntityModificationStatus#EntityModificationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("ModifiedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("modified_by");

                            b1.Property<DateTime?>("ModifiedOnUtc")
                                .ValueGeneratedOnUpdate()
                                .HasColumnType("datetime2")
                                .HasColumnName("modified_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .IsUnique()
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("SYSTEM_IDENTITY_USERS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USERSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserClaim", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ClaimType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("claim_value");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.UserClaim.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityDeletionStatus", "Domain.Aggregates.Identity.UserClaim.EntityDeletionStatus#EntityDeletionStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("DeletedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("deleted_by");

                            b1.Property<DateTime?>("DeletedOnUtc")
                                .HasColumnType("datetime2")
                                .HasColumnName("deleted_on_utc");

                            b1.Property<bool>("IsDeleted")
                                .HasColumnType("bit")
                                .HasColumnName("is_deleted");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityModificationStatus", "Domain.Aggregates.Identity.UserClaim.EntityModificationStatus#EntityModificationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("ModifiedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("modified_by");

                            b1.Property<DateTime?>("ModifiedOnUtc")
                                .ValueGeneratedOnUpdate()
                                .HasColumnType("datetime2")
                                .HasColumnName("modified_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("SYSTEM_IDENTITY_USER_CLAIMS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_CLAIMSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserClientApplication", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("ApplicationId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("application_id");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("UserId");

                    b.ToTable("SYSTEM_LINK_IDENTITY_USER_CLIENT_APPLICATIONS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_LINK_IDENTITY_USER_CLIENT_APPLICATIONSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserMultiFactorSettings", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<bool>("MultiFactorAuthenticatorEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("authenticator_enabled");

                    b.Property<bool>("MultiFactorEmailEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("email_enabled");

                    b.Property<bool>("MultiFactorPasskeysEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("passkeys_enabled");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.ComplexProperty<Dictionary<string, object>>("EntityCreationStatus", "Domain.Aggregates.Identity.UserMultiFactorSettings.EntityCreationStatus#EntityCreationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("CreatedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("created_by");

                            b1.Property<DateTime>("CreatedOnUtc")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("datetime2")
                                .HasColumnName("created_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.ComplexProperty<Dictionary<string, object>>("EntityModificationStatus", "Domain.Aggregates.Identity.UserMultiFactorSettings.EntityModificationStatus#EntityModificationStatus<string>", b1 =>
                        {
                            b1.IsRequired();

                            b1.Property<string>("ModifiedBy")
                                .HasMaxLength(36)
                                .HasColumnType("nvarchar(36)")
                                .HasColumnName("modified_by");

                            b1.Property<DateTime?>("ModifiedOnUtc")
                                .ValueGeneratedOnUpdate()
                                .HasColumnType("datetime2")
                                .HasColumnName("modified_on_utc")
                                .HasDefaultValueSql("GETUTCDATE()");
                        });

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("SYSTEM_IDENTITY_USER_MFA_SETTINGS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_MFA_SETTINGSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserPasskeyChallenge", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("PasskeyChallengeId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("passkey_challenge_id");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("PasskeyChallengeId");

                    b.HasIndex("UserId");

                    b.ToTable("SYSTEM_LINK_IDENTITY_USER_PASSKEY_CHALLENGE", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_LINK_IDENTITY_USER_PASSKEY_CHALLENGEHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserPasskeyCredential", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("id");

                    b.Property<string>("PasskeyCredentialId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("passkey_credential_id");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("PasskeyCredentialId");

                    b.HasIndex("UserId");

                    b.ToTable("SYSTEM_LINK_IDENTITY_USER_PASSKEY_CREDENTIAL", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_LINK_IDENTITY_USER_PASSKEY_CREDENTIALHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserRole", b =>
                {
                    b.Property<string>("RoleId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("role_id");

                    b.Property<string>("UserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("RoleId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("SYSTEM_LINK_IDENTITY_USER_ROLES", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_LINK_IDENTITY_USER_ROLESHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.InboxState", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("Consumed")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ReceiveCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("Received")
                        .HasColumnType("datetime2");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("Id");

                    b.HasIndex("Delivered");

                    b.ToTable("SYSTEM_IDENTITY_INBOX_STATE", (string)null);
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxMessage", b =>
                {
                    b.Property<long>("SequenceNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("SequenceNumber"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid?>("ConversationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DestinationAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("EnqueueTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("FaultAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Headers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("InboxConsumerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InboxMessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InitiatorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OutboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Properties")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ResponseAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("SentTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("SourceAddress")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("SequenceNumber");

                    b.HasIndex("EnqueueTime");

                    b.HasIndex("ExpirationTime");

                    b.HasIndex("OutboxId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[OutboxId] IS NOT NULL");

                    b.HasIndex("InboxMessageId", "InboxConsumerId", "SequenceNumber")
                        .IsUnique()
                        .HasFilter("[InboxMessageId] IS NOT NULL AND [InboxConsumerId] IS NOT NULL");

                    b.ToTable("SYSTEM_IDENTITY_OUTBOX_MESSAGES", (string)null);
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxState", b =>
                {
                    b.Property<Guid>("OutboxId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("Delivered")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSequenceNumber")
                        .HasColumnType("bigint");

                    b.Property<Guid>("LockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("rowversion");

                    b.HasKey("OutboxId");

                    b.HasIndex("Created");

                    b.ToTable("SYSTEM_IDENTITY_OUTBOX_STATE", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.ToTable("SYSTEM_IDENTITY_USER_LOGIN", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_LOGINHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("SYSTEM_IDENTITY_USER_TOKENS", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SYSTEM_IDENTITY_USER_TOKENSHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.RoleClaim", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.Role", "Role")
                        .WithMany("RoleClaims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserClaim", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithMany("UserClaims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserClientApplication", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.ClientApplication", "Application")
                        .WithMany("UserClientApplications")
                        .HasForeignKey("ApplicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithMany("UserClientApplications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserMultiFactorSettings", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithOne("UserMultiFactorSettings")
                        .HasForeignKey("Domain.Aggregates.Identity.UserMultiFactorSettings", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserPasskeyChallenge", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.PasskeyChallenge", "PasskeyChallenge")
                        .WithMany("UserPasskeyChallenges")
                        .HasForeignKey("PasskeyChallengeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithMany("UserPasskeyChallenges")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PasskeyChallenge");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserPasskeyCredential", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.PasskeyCredential", "PasskeyCredential")
                        .WithMany("UserPasskeyCredential")
                        .HasForeignKey("PasskeyCredentialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithMany("UserPasskeyCredentials")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PasskeyCredential");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.UserRole", b =>
                {
                    b.HasOne("Domain.Aggregates.Identity.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Aggregates.Identity.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MassTransit.EntityFrameworkCoreIntegration.OutboxMessage", b =>
                {
                    b.HasOne("MassTransit.EntityFrameworkCoreIntegration.OutboxState", null)
                        .WithMany()
                        .HasForeignKey("OutboxId");

                    b.HasOne("MassTransit.EntityFrameworkCoreIntegration.InboxState", null)
                        .WithMany()
                        .HasForeignKey("InboxMessageId", "InboxConsumerId")
                        .HasPrincipalKey("MessageId", "ConsumerId");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.ClientApplication", b =>
                {
                    b.Navigation("UserClientApplications");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.PasskeyChallenge", b =>
                {
                    b.Navigation("UserPasskeyChallenges");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.PasskeyCredential", b =>
                {
                    b.Navigation("UserPasskeyCredential");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.Role", b =>
                {
                    b.Navigation("RoleClaims");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("Domain.Aggregates.Identity.User", b =>
                {
                    b.Navigation("UserClaims");

                    b.Navigation("UserClientApplications");

                    b.Navigation("UserMultiFactorSettings")
                        .IsRequired();

                    b.Navigation("UserPasskeyChallenges");

                    b.Navigation("UserPasskeyCredentials");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}

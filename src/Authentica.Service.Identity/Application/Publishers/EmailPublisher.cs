using Common.Constants;
using Domain.Contracts;
using MassTransit;
using Microsoft.FeatureManagement;

namespace Application.Publishers;

/// <summary>
/// Publishes a message to the message queue, for confirmation emails, 
/// password reset codes, password reset links and mfa codes.
/// </summary>
public sealed class EmailPublisher : IPublisher
{
    /// <summary>
    /// The application's service provider
    /// </summary>
    public IServiceProvider ServiceProvider { get; }

    /// <summary>
    /// Initalizes a new instance of <see cref="EmailPublisher"/>
    /// </summary>
    public EmailPublisher(IServiceProvider serviceProvider)
    {
        ServiceProvider = serviceProvider;
    }

    /// <inheritdoc/>
    public async Task PublishAsync<TEvent>(TEvent @event,
                                           CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(@event);

        IPublishEndpoint bus = ServiceProvider.GetService<IPublishEndpoint>()!;
        IFeatureManager featureManager = ServiceProvider.GetService<IFeatureManager>()!;

        if (await featureManager!.IsEnabledAsync(FeatureFlagConstants.RabbitMq) ||
            await featureManager!.IsEnabledAsync(FeatureFlagConstants.AzServiceBus))
        {
            await bus.Publish(@event, cancellationToken);
        }
    }
}

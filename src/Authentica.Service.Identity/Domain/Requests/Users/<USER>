using Domain.Attributes;
using Microsoft.AspNetCore.Mvc;

namespace Domain.Requests;

/// <summary>
/// Represents an email confirmation.
/// </summary>
public sealed record ConfirmEmailRequest
{
    /// <summary>
    /// The users email address
    /// </summary>
    [FromQuery(Name = "email")]
    public required string Email { get; init; } = default!;
    /// <summary>
    /// The code to confirm the email address
    /// </summary>
    [FromQuery(Name = "token")]
    [SensitiveData]
    public required string Token { get; init; } = default!;

}

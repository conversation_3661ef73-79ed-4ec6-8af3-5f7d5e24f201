namespace Api.Constants;

public static partial class Routes
{
    /// <summary>
    /// Routes related to user operations.
    /// </summary>
    public static partial class Users
    {
        /// <summary>
        /// Route for logging out using OAuth.
        /// </summary>
        public const string Logout = "users/logout";

        /// <summary>
        /// Route for logging in using OAuth.
        /// </summary>
        public const string Login = "users/login";
        /// <summary>
        /// Route for reading a user by email, only the currently logged in user.
        /// </summary>
        public const string ReadByEmail = "users";
        /// <summary>
        /// Route for creating a new user.
        /// </summary>
        public const string Create = "users/register";
        /// <summary>
        /// Route for deleting a user by email.
        /// </summary>
        public const string DeleteByEmail = "users/delete";
        /// <summary>
        /// Route for confirming user email.
        /// </summary>
        public const string ConfirmEmail = "users/confirm-email";
        /// <summary>
        /// Route for registering/creating a new user.
        /// </summary>
        public const string Register = "users/register";
        /// <summary>
        /// Route for resetting a user's password.
        /// </summary>
        public const string ResetPassword = "users/reset-password";
        /// <summary>
        /// Route for mfa tokens.
        /// </summary>
        public const string Tokens = "users/tokens";
    }
}
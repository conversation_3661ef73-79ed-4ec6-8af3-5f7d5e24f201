using Api.Constants;
using Application.Activities;
using Ardalis.ApiEndpoints;
using Domain.Aggregates.Identity;
using Domain.Contracts.Stores;
using Domain.Requests;
using Domain.Responses;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;

namespace Api.Endpoints.Applications;

/// <summary>
/// Exposes an endpoint where users can read their application by name.
/// </summary>
[Route($"{Routes.BaseRoute.Name}")]
[OutputCache]
public sealed class ReadByNameApplicationEndpoint : EndpointBaseAsync
                                                    .WithRequest<ReadApplicationByNameRequest>
                                                    .WithActionResult
{
    /// <summary>
    /// Gets the service provider used to resolve dependencies.
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    /// Initializes a new instance of <see cref="ReadByNameApplicationEndpoint"/>
    /// </summary>
    /// <param name="services">The application's service provider.</param>
    public ReadByNameApplicationEndpoint(IServiceProvider services)
    {
        Services = services ?? throw new ArgumentNullException(nameof(services));
    }

    /// <summary>
    /// Allows a user to read an application by name.
    /// </summary>
    /// <param name="request">The object which encapsulates the request body.</param>
    /// <param name="cancellationToken">The cancellation token to cancel the operation.</param>
    /// <returns>The result of the requested application.</returns>
    [HttpGet($"{Routes.Applications.ReadByName}")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public override async Task<ActionResult> HandleAsync(ReadApplicationByNameRequest request,
                                                         CancellationToken cancellationToken = default)
    {
        var readStoreResult = Services.GetRequiredService<IApplicationReadStore>();
        var userReadStore = Services.GetRequiredService<IUserReadStore>();
        var user = await userReadStore.GetUserByEmailAsync(User, cancellationToken);
        var activityStore = Services.GetRequiredService<IActivityWriteStore>();

        ClientApplication? app = await readStoreResult.GetClientApplicationByNameAndUserIdAsync(request.Name,
                                                                                                user.User!.Id,
                                                                                                cancellationToken);

        if (app is null)
            return BadRequest();

        ReadApplicationResponse response = new()
        {
            ClientId = app.ClientId,
            CallbackUri = app.CallbackUri,
            Name = app.Name,
            IsDeleted = app.EntityDeletionStatus.IsDeleted,
            DeletedOnUtc = app.EntityDeletionStatus.DeletedOnUtc,
            DeletedBy = app.EntityDeletionStatus.DeletedBy,
            CreatedOnUtc = app.EntityCreationStatus.CreatedOnUtc,
            CreatedBy = app.EntityCreationStatus.CreatedBy,
            ModifiedBy = app.EntityModificationStatus.ModifiedBy,
            ModifiedOnUtc = app.EntityModificationStatus.ModifiedOnUtc

        };

        ReadApplicationByNameActivity activity = new()
        {
            Request = request,
            Response = response
        };

        await activityStore.SaveActivityAsync(activity);

        return Ok(response);
    }
}
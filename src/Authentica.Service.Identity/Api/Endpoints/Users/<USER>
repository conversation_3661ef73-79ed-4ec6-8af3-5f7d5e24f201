using Api.Constants;
using Application.Activities;
using Ardalis.ApiEndpoints;
using Domain.Aggregates.Identity;
using Domain.Contracts;
using Domain.Contracts.Stores;
using Common.Events;
using Domain.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Api.Endpoints.Users;

/// <summary>
/// Exposes an endpoint that allows a user to register.
/// </summary>
[Route($"{Routes.BaseRoute.Name}")]
public sealed class RegisterEndpoint : EndpointBaseAsync
                                       .WithRequest<RegisterRequest>
                                       .WithActionResult
{
    /// <summary>
    /// Gets the service provider used to resolve dependencies.
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    /// Initializes a new instance of <see cref="RegisterEndpoint"/>
    /// </summary> 
    public RegisterEndpoint(IServiceProvider services)
    {
        Services = services ?? throw new ArgumentNullException(nameof(services));
    }

    /// <summary>
    /// Handles the HTTP POST request for creating a new user.
    /// </summary>
    /// <param name="request">The authentication request containing the email and password.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>
    /// <see cref="ActionResult"/> indicating the result of the authentication attempt.
    /// Returns <see cref="StatusCodes.Status201Created"/> if the user is created..
    /// Returns <see cref="StatusCodes.Status409Conflict"/> if the user exists.
    /// Returns <see cref="StatusCodes.Status500InternalServerError"/> in case of an internal server error.
    /// </returns>
    [HttpPost($"{Routes.Users.Create}")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [AllowAnonymous]
    public override async Task<ActionResult> HandleAsync(RegisterRequest request,
                                                         CancellationToken cancellationToken = default)
    {
        var userManager = Services.GetRequiredService<UserManager<User>>();
        var userWriteStore = Services.GetRequiredService<IUserWriteStore>();
        var activityWriteStore = Services.GetRequiredService<IActivityWriteStore>();
        var userMultiFactorStore = Services.GetRequiredService<IUserMultiFactorWriteStore>();
        var publisher = Services.GetRequiredService<IPublisher>();

        var existingUser = await userManager.FindByEmailAsync(request.Email);

        if (existingUser is not null && !existingUser.EntityDeletionStatus.IsDeleted)
            return StatusCode(StatusCodes.Status409Conflict, "User is deleted, or already exists.");

        var result = await userWriteStore.CreateUserAsync(request, cancellationToken);

        await userMultiFactorStore.CreateAsync(result.User.Id);

        // Enable email two factor by default.
        await userManager.SetTwoFactorEnabledAsync(result.User, true);
        await userMultiFactorStore.SetEmailAsync(true, result.User.Id);

        RegisterActivity activity = new()
        {
            Payload = request
        };

        await activityWriteStore.SaveActivityAsync(activity);

        if (result.Errors.Any())
            return StatusCode(StatusCodes.Status500InternalServerError, result.Errors.First().Description);

        if (!await userManager.IsInRoleAsync(result.User, RoleDefaults.User))
            await userManager.AddToRoleAsync(result.User, RoleDefaults.User);

        UserRegistered @event = new(request.Email,
                                      DateTime.UtcNow);
        
        await publisher.PublishAsync(@event, cancellationToken);


        return StatusCode(StatusCodes.Status201Created);
    }
}

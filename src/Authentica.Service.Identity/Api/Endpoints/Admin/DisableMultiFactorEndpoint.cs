using Api.Constants;
using Application.Activities;
using Ardalis.ApiEndpoints;
using Domain.Aggregates.Identity;
using Domain.Contracts;
using Domain.Contracts.Stores;
using Common.Events;
using Domain.Requests;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Api.Endpoints.Admin;

/// <summary>
/// Endpoint for reading all applications and returning their responses.
/// </summary>
[Route($"{Routes.BaseRoute.Name}")]
public sealed class DisableMultiFactorEndpoint : EndpointBaseAsync
                                                 .WithRequest<DisableMultiFactorRequest>
                                                 .WithActionResult
{
    /// <summary>
    /// Gets the service provider used to resolve dependencies.
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DisableMultiFactorEndpoint"/> class.
    /// </summary>
    /// <param name="services">The service provider used to resolve dependencies.</param>
    public DisableMultiFactorEndpoint(IServiceProvider services)
    {
        Services = services;
    }

    /// <summary>
    /// Handles disabling mfa for a given user.
    /// </summary>
    /// <param name="request">The object which encapsulates the request.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>An <see cref="ActionResult"/> indicating the result of the operation.</returns>
    [HttpPost($"{Routes.Admin.DisableMultiFactor}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = RoleDefaults.Admin)]
    public override async Task<ActionResult> HandleAsync(DisableMultiFactorRequest request,
                                                         CancellationToken cancellationToken = default)
    {
        var userManager = Services.GetRequiredService<UserManager<User>>();
        var activityStore = Services.GetRequiredService<IActivityWriteStore>();
        var multiFactorWriteStore = Services.GetRequiredService<IUserMultiFactorWriteStore>();
        var passkeyStore = Services.GetRequiredService<IPasskeyCredentialWriteStore>();
        var publisher = Services.GetRequiredService<IPublisher>();

        var user = await userManager.FindByEmailAsync(request.Email);

        if (user is null)
            return BadRequest();

        // Disable MFA within the SYSTEM_IDENTITY_USERS table.
        await userManager.SetTwoFactorEnabledAsync(user, false);

        await multiFactorWriteStore.SetPasskeysAsync(false, user.Id);
        await multiFactorWriteStore.SetEmailAsync(false, user.Id);
        await multiFactorWriteStore.SetAutheticatorAsync(false, user.Id);

        // TODO: Now delete all passkeys.


        DisableMultiFactorActivity activity = new()
        {
            Payload = request
        };

        await activityStore.SaveActivityAsync(activity);

        AdminDisabledUserMfa @event = new(request.Email,
                                          DateTime.UtcNow,
                                          User.Identity?.Name ?? "Unknown");
        
        await publisher.PublishAsync(@event, cancellationToken);

        return Ok();
    }
}
{"profiles": {"https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://localhost:4317"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7171;http://localhost:5252"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json"}
﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.Kubernetes" Version="7.0.2" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.12.1" />
	 <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

</Project>
services:
  
  # authentica.service.identity:
  #   image: ${DOCKER_REGISTRY-}authenticaserviceidentity
  #   build:
  #     context: .
  #     dockerfile: src/Dockerfile.service.identity
  #   environment: 
  #     OTEL_EXPORTER_OTLP_ENDPOINT: "http://aspire-dashboard:4317"
  #     ASPNETCORE_HTTPS_PORTS: 7171
  #     ASPNETCORE_ENVIRONMENT: "Development"

  # authentica.workerservice.email:
  #   image: ${DOCKER_REGISTRY-}authenticaworkerserviceemail
  #   build:
  #     context: .
  #     dockerfile: src/Dockerfile.workerservice.email
  #   environment: 
  #     OTEL_EXPORTER_OTLP_ENDPOINT: "http://aspire-dashboard:4317"

  rabbitmq:
    hostname: "rabbitmq"
    container_name: rabbitmq
    restart: unless-stopped
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "15671:15671"
      - "15691:15691"
      - "15692:15692"
      - "25672:25672"
      - "4369:4369"
      - "5671:5671"
      - "5672:5672"
      - "15672:15672"
  
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: sqlserver2022
    environment:
      SA_PASSWORD: "YourStrong@Passw0rd"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserverdata:/var/opt/mssql
    restart: always
  
  redis:
    image: redis:latest
    container_name: test-redis
    ports:
      - "5002:6379"
    restart: always
    
  aspire-dashboard:
    image: mcr.microsoft.com/dotnet/aspire-dashboard:9.0
    ports:
      - "18888:18888"
      - "4317:18889"
    container_name: aspire-dashboard
    restart: unless-stopped

volumes:
  sqlserverdata:
    driver: local


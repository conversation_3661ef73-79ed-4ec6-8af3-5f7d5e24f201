# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 7171

# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Authentica.Service.Identity/Authentica.Service.Identity.csproj", "Authentica.Service.Identity/"]
COPY ["Authentica.Common/Authentica.Common.csproj", "Authentica.Common/"]
RUN dotnet restore "./Authentica.Service.Identity/Authentica.Service.Identity.csproj"
COPY . .
WORKDIR "/src/Authentica.Service.Identity"
RUN dotnet build "./Authentica.Service.Identity.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Authentica.Service.Identity.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENV ASPNETCORE_URLS=https://+:7171
COPY Authentica.Service.Identity/cert.pfx /app/cert.pfx
RUN chmod 644 /app/cert.pfx 
ENV ASPNETCORE_Kestrel__Certificates__Default__Password=YourSecurePassword
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/app/cert.pfx
USER app
ENTRYPOINT ["dotnet", "Authentica.Service.Identity.dll"]
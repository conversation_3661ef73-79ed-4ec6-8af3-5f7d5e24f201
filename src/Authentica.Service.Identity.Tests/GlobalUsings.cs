global using Api.Constants;
global using Application.Factories;
global using Application.Providers;
global using Application.Publishers;
global using Application.Results;
global using Application.Stores;
global using Authentica.Common;
global using Authentica.Service.Identity.Tests.Mocks;
global using Domain.Aggregates.Identity;
global using Domain.Requests;
global using Domain.ValueObjects;
global using DotNet.Testcontainers.Builders;
global using MassTransit;
global using Microsoft.AspNetCore.Authentication;
global using Microsoft.AspNetCore.Hosting;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Identity;
global using Microsoft.AspNetCore.Mvc.Testing;
global using Microsoft.AspNetCore.TestHost;
global using Microsoft.EntityFrameworkCore.ChangeTracking;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.DependencyInjection.Extensions;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using Microsoft.FeatureManagement;
global using Microsoft.IdentityModel.Tokens;
global using Moq;
global using Persistence.Contexts;
global using System.IdentityModel.Tokens.Jwt;
global using System.Net;
global using System.Net.Http.Headers;
global using System.Net.Http.Json;
global using System.Security.Claims;
global using System.Text.Encodings.Web;
global using System.Text.Json;
global using Testcontainers.MsSql;
global using Testcontainers.Redis;
global using Application.BackgroundServices;
global using Application.Exceptions;
global using Domain.Contracts.Providers;
global using Domain.Contracts.Stores;
global using Persistence.Seed;

# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  
env:
  # Use docker.io for Docker Hub if empty
  REGISTRY: ghcr.io
  IMAGE_NAME: "Authentica.WorkerService.Email"

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x
    - name: Build & Restore
      run: dotnet build --configuration Release src/Authentica.WorkerService.Email/Authentica.WorkerService.Email.csproj
    - name: Test
      run: dotnet test --collect:"XPlat Code Coverage" src/Authentica.WorkerService.Email.Tests/Authentica.WorkerService.Email.Tests.csproj
    - name: Publish
      run: dotnet publish src/Authentica.WorkerService.Email/Authentica.WorkerService.Email.csproj -c Release

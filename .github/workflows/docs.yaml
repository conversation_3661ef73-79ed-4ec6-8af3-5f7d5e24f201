name: Deploy DocFX to GitHub Pages

on:
  push:
    branches: [ main ]  # Trigger on push to main branch

jobs:
  generate-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Setup .NET Core
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: 8.0.x  # Adjust version as needed

    - name: Install DocFX
      run: dotnet tool install -g docfx

    - name: Build Documentation
      run: docfx docfx.json

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./_site
        force_orphan: true